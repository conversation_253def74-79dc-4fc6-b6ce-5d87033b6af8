//+------------------------------------------------------------------+
//|                                        SimpleVolumesStrategy.mqh |
//|                                      Copyright 2024, <PERSON><PERSON> |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, <PERSON><PERSON>"
#property link      "https://www.mql5.com/en/articles/15330"
#property version   "1.09"

#include "NewBarEvent.mqh"
#include "VirtualStrategy.mqh"

/** Strategy

Strategy input:
   - Symbol
   - Period
   - Number of candles for volume averaging (K)
   - Relative deviation from the average for opening the first order (D)
   - Relative deviation from the average for opening the second and subsequent orders (D_add)
   - Distance from price to pending order
   - Stop Loss (in points)
   - Take Profit (in points)
   - Expiration time of pending orders (in minutes)
   - Maximum number of simultaneously open orders (N_max)
   - Single order volume

Find the number of open orders and positions (N).
If it is less than N_max, then:
   - calculate the average tick volume for the last K closed candles, get the V_avr value.
   - If the V > V_avr * (1 + D + N * D_add) condition is met, then:
        - determine the direction of price change on the current candle: if the price has increased,
          set BUY, BUY_STOP or BUY_LIMIT, otherwise - SELL, SELL_STOP or SELL_LIMIT
        - set a market or pending order based on the distance
          (0 - market, >0 - pending stop, <0 - pending limit),
          of the expiration time and StopLoss/TakeProfit levels set in the parameters.
*/


//+------------------------------------------------------------------+
//| Trading strategy using tick volumes                              |
//+------------------------------------------------------------------+
class CSimpleVolumesStrategy : public CVirtualStrategy {
protected:
   string            m_symbol;         // Symbol (trading instrument)
   ENUM_TIMEFRAMES   m_timeframe;      // Chart period (timeframe)

   //---  Open signal parameters
   int               m_signalPeriod;       // Number of candles for volume averaging
   double            m_signalDeviation;    // Relative deviation from the average to open the first order
   double            m_signaAddlDeviation; // Relative deviation from the average for opening the second and subsequent orders

   //---  Pending order parameters
   int               m_openDistance;       // Distance from price to pending order
   double            m_stopLevel;          // Stop Loss (in points)
   double            m_takeLevel;          // Take Profit (in points)
   int               m_ordersExpiration;   // Pending order expiration time (in minutes)

   //---  Money management parameters
   int               m_maxCountOfOrders;   // Max number of simultaneously open orders

   CSymbolInfo       m_symbolInfo;          // Object for obtaining data on the symbol properties

   int               m_iVolumesHandle;      // Tick volume indicator handle
   double            m_volumes[];           // Receiver array of indicator values (volumes themselves)

   //--- Methods
   int               SignalForOpen();     // Signal for opening pending orders
   void              OpenBuyOrder();      // Open the BUY_STOP order
   void              OpenSellOrder();     // Open the SELL_STOP order
   double            ArrayAverage(
      const double &array[]);             // Average value of the number array

public:
   //--- Public methods
                     CSimpleVolumesStrategy(string p_params); // Constructor

   virtual string    operator~() override;         // Convert object to string

   virtual void      Tick() override;              // OnTick event handler

   // Replace symbol names
   virtual bool      SymbolsReplace(CHashMap<string, string> &p_symbolsMap);
};


//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CSimpleVolumesStrategy::CSimpleVolumesStrategy(string p_params) {
   m_params = p_params;
   m_symbol = ReadString(p_params);
   m_timeframe = (ENUM_TIMEFRAMES) ReadLong(p_params);
   m_signalPeriod = (int) ReadLong(p_params);
   m_signalDeviation = ReadDouble(p_params);
   m_signaAddlDeviation = ReadDouble(p_params);
   m_openDistance = (int) ReadLong(p_params);
   m_stopLevel = ReadDouble(p_params);
   m_takeLevel = ReadDouble(p_params);
   m_ordersExpiration = (int) ReadLong(p_params);
   m_maxCountOfOrders = (int) ReadLong(p_params);

   if(IsValid()) {
      CVirtualReceiver::Get(GetPointer(this), m_orders, m_maxCountOfOrders);

      // Load the indicator to get tick volumes
      m_iVolumesHandle = iVolumes(m_symbol, m_timeframe, VOLUME_TICK);

      // If the indicator is loaded successfully
      if(m_iVolumesHandle != INVALID_HANDLE) {

         // Set the size of the tick volume receiving array and the required addressing
         ArrayResize(m_volumes, m_signalPeriod);
         ArraySetAsSeries(m_volumes, true);

         // Register the event handler for a new bar on the minimum timeframe
         IsNewBar(m_symbol, PERIOD_M1);
      } else {
         // Otherwise, set the object state to invalid
         SetInvalid(__FUNCTION__, "Can't load iVolumes()");
      }
   }
}

//+------------------------------------------------------------------+
//| Convert an object to a string                                    |
//+------------------------------------------------------------------+
string CSimpleVolumesStrategy::operator~() {
   return StringFormat("%s(%s)", typename(this), m_params);
}

//+------------------------------------------------------------------+
//| "Tick" event handler function                                    |
//+------------------------------------------------------------------+
void CSimpleVolumesStrategy::Tick() override {
// If their number is less than allowed
   if(m_ordersTotal < m_maxCountOfOrders) {
      // Get an open signal
      int signal = SignalForOpen();

      if(signal == 1 /* || m_ordersTotal < 1 */) {          // If there is a buy signal, then
         OpenBuyOrder();         // open the BUY_STOP order
      } else if(signal == -1) {  // If there is a sell signal, then
         OpenSellOrder();        // open the SELL_STOP order
      }
   }
}

//+------------------------------------------------------------------+
//| Replace symbol names                                             |
//+------------------------------------------------------------------+
bool CSimpleVolumesStrategy::SymbolsReplace(CHashMap<string, string> &p_symbolsMap) {
// If there is a key in the glossary that matches the current symbol
   if(p_symbolsMap.ContainsKey(m_symbol)) {
      string targetSymbol; // Target symbol

      // If the target symbol for the current one is successfully retrieved from the glossary
      if(p_symbolsMap.TryGetValue(m_symbol, targetSymbol)) {
         // Update the current symbol
         m_symbol = targetSymbol;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Signal for opening pending orders                                |
//+------------------------------------------------------------------+
int CSimpleVolumesStrategy::SignalForOpen() {
// By default, there is no signal
   int signal = 0;

// Copy volume values from the indicator buffer to the receiving array
   int res = CopyBuffer(m_iVolumesHandle, 0, 0, m_signalPeriod, m_volumes);

// If the required amount of numbers have been copied
   if(res == m_signalPeriod) {
      // Calculate their average value
      double avrVolume = ArrayAverage(m_volumes);

      // If the current volume exceeds the specified level, then
      if(m_volumes[0] > avrVolume * (1 + m_signalDeviation + m_ordersTotal * m_signaAddlDeviation)) {
         // if the opening price of the candle is less than the current (closing) price, then
         if(iOpen(m_symbol, m_timeframe, 0) < iClose(m_symbol, m_timeframe, 0)) {
            signal = 1; // buy signal
         } else {
            signal = -1; // otherwise, sell signal
         }
      }
   }

   return signal;
}

//+------------------------------------------------------------------+
//| Open BUY order                                                   |
//+------------------------------------------------------------------+
void CSimpleVolumesStrategy::OpenBuyOrder() {
// Update symbol current price data
   m_symbolInfo.Name(m_symbol);
   m_symbolInfo.RefreshRates();

// Retrieve the necessary symbol and price data
   double point = m_symbolInfo.Point();
   int digits = m_symbolInfo.Digits();
   double bid = m_symbolInfo.Bid();
   double ask = m_symbolInfo.Ask();
   int spread = m_symbolInfo.Spread();

// Let's make sure that the opening distance is not less than the spread
   int distance = MathMax(MathAbs(m_openDistance), spread) * (m_openDistance < 0 ? -1 : 1);

// Opening price
   double price = ask + distance * point;

// StopLoss and TakeProfit levels
   double sl = NormalizeDouble(price - m_stopLevel * point, digits);
   double tp = NormalizeDouble(price + (m_takeLevel + spread) * point, digits);

// Expiration time
   datetime expiration = TimeCurrent() + m_ordersExpiration * 60;

   bool res = false;
   for(int i = 0; i < m_maxCountOfOrders; i++) {   // Iterate through all virtual positions
      if(!m_orders[i].IsOpen()) {                  // If we find one that is not open, then open it
         if(m_openDistance > 0) {
            // Set SELL STOP pending order
            res = m_orders[i].Open(m_symbol, ORDER_TYPE_BUY_STOP, m_fixedLot,
                                   NormalizeDouble(price, digits),
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits),
                                   "", expiration);

         } else if(m_openDistance < 0) {
            // Set SELL LIMIT pending order
            res = m_orders[i].Open(m_symbol, ORDER_TYPE_BUY_LIMIT, m_fixedLot,
                                   NormalizeDouble(price, digits),
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits),
                                   "", expiration);

         } else {
            // Open a virtual SELL position
            res = m_orders[i].Open(m_symbol, ORDER_TYPE_BUY, m_fixedLot,
                                   0,
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits));

         }
         break; // and exit
      }
   }

   if(!res) {
      PrintFormat(__FUNCTION__" | ERROR opening BUY virtual order", 0);
   }
}

//+------------------------------------------------------------------+
//| Open SELL order                                                  |
//+------------------------------------------------------------------+
void CSimpleVolumesStrategy::OpenSellOrder() {
// Update symbol current price data
   m_symbolInfo.Name(m_symbol);
   m_symbolInfo.RefreshRates();

// Retrieve the necessary symbol and price data
   double point = m_symbolInfo.Point();
   int digits = m_symbolInfo.Digits();
   double bid = m_symbolInfo.Bid();
   double ask = m_symbolInfo.Ask();
   int spread = m_symbolInfo.Spread();

// Let's make sure that the opening distance is not less than the spread
   int distance = MathMax(MathAbs(m_openDistance), spread) * (m_openDistance < 0 ? -1 : 1);

// Opening price
   double price = bid - distance * point;

// StopLoss and TakeProfit levels
   double sl = NormalizeDouble(price + m_stopLevel * point, digits);
   double tp = NormalizeDouble(price - (m_takeLevel + spread) * point, digits);

// Expiration time
   datetime expiration = TimeCurrent() + m_ordersExpiration * 60;

   bool res = false;
   for(int i = 0; i < m_maxCountOfOrders; i++) {   // Iterate through all virtual positions
      if(!m_orders[i].IsOpen()) {                  // If we find one that is not open, then open it
         if(m_openDistance > 0) {
            // Set SELL STOP pending order
            res = m_orders[i].Open(m_symbol, ORDER_TYPE_SELL_STOP, m_fixedLot,
                                   NormalizeDouble(price, digits),
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits),
                                   "", expiration);

         } else if(m_openDistance < 0) {
            // Set SELL LIMIT pending order
            res = m_orders[i].Open(m_symbol, ORDER_TYPE_SELL_LIMIT, m_fixedLot,
                                   NormalizeDouble(price, digits),
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits),
                                   "", expiration);

         } else {
            // Open a virtual SELL position

            res = m_orders[i].Open(m_symbol, ORDER_TYPE_SELL, m_fixedLot,
                                   0,
                                   NormalizeDouble(sl, digits),
                                   NormalizeDouble(tp, digits));
         }
         break;   // and exit
      }
   }

   if(!res) {
      PrintFormat(__FUNCTION__" | ERROR opening SELL virtual order", 0);
   }
}


//+------------------------------------------------------------------+
//| Average value of the array of numbers from the second element    |
//+------------------------------------------------------------------+
double CSimpleVolumesStrategy::ArrayAverage(const double &array[]) {
   double s = 0;
   int total = ArraySize(array) - 1;
   for(int i = 1; i <= total; i++) {
      s += array[i];
   }

   return s / MathMax(1, total);
}
//+------------------------------------------------------------------+
