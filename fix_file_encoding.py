#!/usr/bin/env python3
"""
Fix file encoding issues in MQL5 files
"""
import os
import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def fix_file_encoding(file_path):
    """Fix encoding issues in a single file."""
    
    print(f"🔧 Fixing encoding for: {file_path.name}")
    
    # Try different encodings
    encodings = ['utf-16', 'utf-16-le', 'utf-16-be', 'utf-8', 'cp1252', 'latin1']
    
    content = None
    used_encoding = None
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            used_encoding = encoding
            print(f"  ✅ Successfully read with {encoding}")
            break
        except Exception as e:
            print(f"  ❌ Failed with {encoding}: {e}")
            continue
    
    if content is None:
        print(f"  ❌ Could not read {file_path.name} with any encoding")
        return False
    
    # Clean up the content if it has weird characters
    if content.startswith('��') or ' / / ' in content or ' c l a s s ' in content:
        print(f"  🔧 Removing BOM and fixing spaced characters")
        content = content.replace('��', '')

        # Fix spaced characters - more comprehensive
        import re

        # Remove spaces between individual characters
        content = re.sub(r'(\w)\s+(\w)', r'\1\2', content)

        # Fix specific patterns
        replacements = {
            ' / / ': '//',
            ' + - ': '+--',
            ' - - ': '--',
            ' | ': '|',
            ' # ': '#',
            ' = ': '=',
            ' ; ': ';',
            ' , ': ',',
            ' ( ': '(',
            ' ) ': ')',
            ' { ': '{',
            ' } ': '}',
            ' [ ': '[',
            ' ] ': ']',
            ' < ': '<',
            ' > ': '>',
            ' : ': ':',
            ' . ': '.',
            ' + ': '+',
            ' - ': '-',
            ' * ': '*',
            ' & ': '&',
            ' ! ': '!',
            ' ? ': '?'
        }

        for old, new in replacements.items():
            content = content.replace(old, new)

        # Fix keywords that got spaced out
        keywords = [
            'include', 'property', 'class', 'public', 'private', 'protected',
            'virtual', 'override', 'void', 'int', 'double', 'string', 'bool',
            'return', 'if', 'else', 'for', 'while', 'const', 'static',
            'enum', 'struct', 'namespace', 'using', 'template', 'typename',
            'true', 'false', 'null', 'this', 'new', 'delete'
        ]

        for keyword in keywords:
            spaced_keyword = ' '.join(keyword)
            content = content.replace(spaced_keyword, keyword)
    
    # Write back as UTF-8
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Fixed and saved as UTF-8")
        return True
    except Exception as e:
        print(f"  ❌ Failed to write: {e}")
        return False

def fix_all_mql_files():
    """Fix encoding for all MQL5 files."""
    
    print("🔧 Fixing MQL5 File Encoding Issues")
    print("=" * 50)
    
    # Files to fix in development directory
    dev_files = [
        "SimpleVolumesStrategy.mqh",
        "MQL5/Experts/SimpleVolumesStage1.mq5",
        "MQL5/Experts/SimpleVolumesExpert.mq5",
        "MQL5/Experts/SimpleVolumesStage2.mq5",
        "MQL5/Experts/SimpleVolumesStage3.mq5"
    ]
    
    for file_rel_path in dev_files:
        file_path = Path(file_rel_path)
        if file_path.exists():
            fix_file_encoding(file_path)
        else:
            print(f"❌ File not found: {file_path}")
    
    # Also fix files in terminal if they exist
    terminal_files = [
        "Include/MultiCurrency/SimpleVolumesStrategy.mqh",
        "Experts/SimpleVolumesStage1.mq5",
        "Experts/SimpleVolumesExpert.mq5",
        "Experts/SimpleVolumesStage2.mq5",
        "Experts/SimpleVolumesStage3.mq5"
    ]
    
    for file_rel_path in terminal_files:
        file_path = Path(MT5_TERMINAL_PATH) / file_rel_path
        if file_path.exists():
            fix_file_encoding(file_path)
        else:
            print(f"❌ Terminal file not found: {file_path}")

def copy_fixed_files():
    """Copy fixed files to terminal."""
    
    print("\n📁 Copying Fixed Files to Terminal")
    print("=" * 40)
    
    # Copy SimpleVolumesStrategy.mqh
    src = Path("SimpleVolumesStrategy.mqh")
    dst = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "SimpleVolumesStrategy.mqh"
    
    if src.exists():
        dst.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(src, dst)
        print(f"✅ Copied {src.name} to terminal")
    
    # Copy Expert files
    expert_files = [
        "SimpleVolumesStage1.mq5",
        "SimpleVolumesExpert.mq5", 
        "SimpleVolumesStage2.mq5",
        "SimpleVolumesStage3.mq5"
    ]
    
    for expert_file in expert_files:
        src = Path("MQL5/Experts") / expert_file
        dst = Path(MT5_TERMINAL_PATH) / "Experts" / expert_file
        
        if src.exists():
            shutil.copy2(src, dst)
            print(f"✅ Copied {expert_file} to terminal")

if __name__ == "__main__":
    fix_all_mql_files()
    copy_fixed_files()
    
    print("\n🎉 File encoding fix completed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Recompile SimpleVolumesStage1.mq5")
    print("3. Check for compilation errors")
    print("4. Re-run the optimization")
