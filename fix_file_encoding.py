#!/usr/bin/env python3
"""
Fix file encoding issues in MQL5 files
"""
import os
import shutil
from pathlib import Path

# Your MT5 Terminal Path
MT5_TERMINAL_PATH = r"C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\7BC3F33EDFDBDBDBADB45838B9A2D03F\MQL5"

def aggressive_character_cleanup(content, encoding_info):
    """Aggressively clean up corrupted character encoding."""

    print(f"  🧹 Performing aggressive character cleanup")

    import re

    # Remove BOM characters
    content = content.replace('\ufeff', '')  # UTF-8 BOM
    content = content.replace('��', '')      # Common corruption

    # Handle severely corrupted UTF-16 content
    if 'utf-16' in encoding_info.lower():
        print("  🔧 Fixing UTF-16 corruption")

        # Remove null bytes that often appear in corrupted UTF-16
        content = content.replace('\x00', '')

        # Fix common UTF-16 corruption patterns
        content = re.sub(r'[\u2000-\u206F\u2E00-\u2E7F\u3000-\u303F]', ' ', content)  # Various spaces and punctuation
        content = re.sub(r'[\uFFF0-\uFFFF]', '', content)  # Remove replacement characters

        # Fix spaced out characters (common in UTF-16 corruption)
        content = re.sub(r'(\w)\s+(\w)(?=\s+\w)', r'\1\2', content)

    # Fix common character corruption patterns
    replacements = {
        # Comment patterns
        '⼯': '//',
        '⁼': '|',
        '⴫': '+',
        'ⴭ': '-',
        '⬭': '+',
        '਍': '\n',
        'ൟ': '',
        'ഊ': '\n',
        'ഢ': '"',
        'ബ': '',
        '紊': '\n',
        '簠': ' |',
        '簯': '|',
        '†': ' ',
        '∠': '"',
        '≳': '"',
        '≮': '"',
        '⤠': ')',
        '⥳': ')',
        '⥟': ')',
        '⥤': ')',
        '⥢': ')',
        '⡤': '(',
        '⡲': '(',
        '⡳': '(',
        '⡮': '(',
        '⡥': '(',
        '⡦': '(',
        '⡩': '(',
        '⡯': '(',
        '⡡': '(',
        '⡣': '(',
        '⡴': '(',
        '⡰': '(',
        '⡬': '(',
        '⡨': '(',
        '⡢': '(',
        '⡧': '(',
        '⡭': '(',
        '⡊': '(',
        '⡈': '(',
        '⡄': '(',
        '⡀': '(',

        # Fix spaced patterns
        ' / / ': '//',
        ' + - ': '+--',
        ' - - ': '--',
        ' | ': '|',
        ' # ': '#',
        ' = ': '=',
        ' ; ': ';',
        ' , ': ',',
        ' ( ': '(',
        ' ) ': ')',
        ' { ': '{',
        ' } ': '}',
        ' [ ': '[',
        ' ] ': ']',
        ' < ': '<',
        ' > ': '>',
        ' : ': ':',
        ' . ': '.',
        ' + ': '+',
        ' - ': '-',
        ' * ': '*',
        ' & ': '&',
        ' ! ': '!',
        ' ? ': '?'
    }

    for old, new in replacements.items():
        content = content.replace(old, new)

    # Fix keywords that got spaced out
    keywords = [
        'include', 'property', 'class', 'public', 'private', 'protected',
        'virtual', 'override', 'void', 'int', 'double', 'string', 'bool',
        'return', 'if', 'else', 'for', 'while', 'const', 'static',
        'enum', 'struct', 'namespace', 'using', 'template', 'typename',
        'true', 'false', 'null', 'this', 'new', 'delete', 'input',
        'sinput', 'extern', 'define', 'undef', 'ifdef', 'ifndef',
        'endif', 'pragma', 'copyright', 'link', 'version', 'description'
    ]

    for keyword in keywords:
        spaced_keyword = ' '.join(keyword)
        content = content.replace(spaced_keyword, keyword)

    # Clean up excessive whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Multiple blank lines
    content = re.sub(r'[ \t]+', ' ', content)  # Multiple spaces/tabs
    content = re.sub(r' +\n', '\n', content)  # Trailing spaces

    print(f"  ✅ Character cleanup completed")
    return content

def fix_file_encoding(file_path):
    """Fix encoding issues in a single file with aggressive recovery."""

    print(f"🔧 Fixing encoding for: {file_path.name}")

    # First try to read as binary and detect encoding
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read()

        print(f"  📊 File size: {len(raw_data)} bytes")

        # Check for BOM markers
        if raw_data.startswith(b'\xff\xfe'):
            print("  🔍 Detected UTF-16 LE BOM")
            encoding = 'utf-16-le'
        elif raw_data.startswith(b'\xfe\xff'):
            print("  🔍 Detected UTF-16 BE BOM")
            encoding = 'utf-16-be'
        elif raw_data.startswith(b'\xef\xbb\xbf'):
            print("  🔍 Detected UTF-8 BOM")
            encoding = 'utf-8-sig'
        else:
            print("  🔍 No BOM detected, trying multiple encodings")
            encoding = None
    except Exception as e:
        print(f"  ❌ Could not read file as binary: {e}")
        return False

    # Try different encodings with error handling
    encodings_to_try = []
    if encoding:
        encodings_to_try.append(encoding)
    encodings_to_try.extend(['utf-16-le', 'utf-16-be', 'utf-16', 'utf-8', 'cp1252', 'latin1'])

    content = None
    used_encoding = None

    for enc in encodings_to_try:
        try:
            # Try with different error handling strategies
            for errors in ['strict', 'ignore', 'replace']:
                try:
                    content = raw_data.decode(enc, errors=errors)
                    used_encoding = f"{enc} (errors={errors})"
                    print(f"  ✅ Successfully decoded with {used_encoding}")
                    break
                except Exception:
                    continue
            if content:
                break
        except Exception as e:
            print(f"  ❌ Failed with {enc}: {e}")
            continue

    if content is None:
        print(f"  ❌ Could not decode {file_path.name} with any encoding")
        return False
    
    # Aggressive character cleanup
    content = aggressive_character_cleanup(content, used_encoding)
    
    # Write back as UTF-8
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Fixed and saved as UTF-8")
        return True
    except Exception as e:
        print(f"  ❌ Failed to write: {e}")
        return False

def fix_all_mql_files():
    """Fix encoding for all MQL5 files."""
    
    print("🔧 Fixing MQL5 File Encoding Issues")
    print("=" * 50)
    
    # Files to fix in development directory
    dev_files = [
        "SimpleVolumesStrategy.mqh",
        "MQL5/Experts/SimpleVolumesStage1.mq5",
        "MQL5/Experts/SimpleVolumesExpert.mq5",
        "MQL5/Experts/SimpleVolumesStage2.mq5",
        "MQL5/Experts/SimpleVolumesStage3.mq5"
    ]
    
    for file_rel_path in dev_files:
        file_path = Path(file_rel_path)
        if file_path.exists():
            fix_file_encoding(file_path)
        else:
            print(f"❌ File not found: {file_path}")
    
    # Also fix files in terminal if they exist
    terminal_files = [
        "Include/MultiCurrency/SimpleVolumesStrategy.mqh",
        "Experts/SimpleVolumesStage1.mq5",
        "Experts/SimpleVolumesExpert.mq5",
        "Experts/SimpleVolumesStage2.mq5",
        "Experts/SimpleVolumesStage3.mq5"
    ]
    
    for file_rel_path in terminal_files:
        file_path = Path(MT5_TERMINAL_PATH) / file_rel_path
        if file_path.exists():
            fix_file_encoding(file_path)
        else:
            print(f"❌ Terminal file not found: {file_path}")

def create_clean_template_if_needed(file_path):
    """Create a clean template file if the original is too corrupted."""

    if "SimpleVolumesStage1.mq5" in str(file_path):
        print(f"  🔧 Creating clean template for {file_path.name}")

        template_content = '''//+------------------------------------------------------------------+
//|                                        SimpleVolumesStage1.mq5 |
//|                                      Copyright 2024, Yuriy Bykov |
//|                            https://www.mql5.com/en/users/antekov |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Yuriy Bykov"
#property link      "https://www.mql5.com/en/articles/15330"
#property description "The EA opens a market or pending order when"
#property description "the candle tick volume exceeds the average volume in the direction of the current candle."
#property description "If orders have not yet turned into positions, they are deleted at expiration time."
#property description "Open positions are closed only by SL or TP."

#define __VERSION__ "1.18"
#property version   __VERSION__

#include "VirtualAdvisor.mqh"
#include "SimpleVolumesStrategy.mqh"
#include "Database.mqh"
#include "VirtualFactory.mqh"

//+------------------------------------------------------------------+
//| Input parameters                                                 |
//+------------------------------------------------------------------+
sinput int    idTask        = 0;    //- Optimization task ID
sinput string fileName_     = "database.sqlite"; //- File with the main database

input group "= Opening signal parameters"
input int    signalPeriod_     = 130; // Number of candles for volume averaging
input double signalDeviation_  = 0.9; // Relative deviation from the average to open the first order
input double signaAddlDeviation = 1.4; // Relative deviation from the average for opening the second and subsequent orders

input group "= Pending order parameters"
input int    openDistance = 231; // Distance from price to pending order
input double stopLevel    = 3750; // Stop Loss (in points)
input double takeLevel    = 50; // Take Profit (in points)
input int    ordersExpiration_ = 600; // Pending order expiration time (in minutes)

input group "= Money management parameters"
input int    maxCountOfOrders = 3; // Maximum number of simultaneously open orders

/*input group "= EA parameters"
ulong        magic         = 27811; // Magic
double       fixedBalance_ = 10000;
double       scale_        = 1;*/

datetime fromDate = TimeCurrent();

CAdvisor    *expert;        // Pointer to the EA object

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   CMoney::FixedBalance(fixedBalance_);
   CMoney::DepoPercent(1.0);

   // Connect to the main database
   DB::Test(fileName_);

   // Prepare the initialization string for a single strategy instance
   string strategyParams = StringFormat(
                              "class CSimpleVolumesStrategy|%s|%d|%d|%.2f|%.2f|%d|%.2f|%.2f|%d|%d",
                              Symbol(), Period(),
                              signalPeriod_, signalDeviation_, signaAddlDeviation,
                              openDistance, stopLevel, takeLevel, ordersExpiration_,
                              maxCountOfOrders
                           );

   // Prepare the initialization string for a group with one strategy instance
   string groupParams = StringFormat(
                           "class CVirtualStrategyGroup|\\n"
                           "   %s\\n"
                           "|",
                           strategyParams
                        );

   // Prepare the initialization string for the risk manager
   string riskManagerParams = StringFormat(
                                 "class CVirtualRiskManager|\\n"
                                 "   0,0,0,0,0,0,0,0,0,0,0,0\\n"
                                 "|",
                                 0
                              );

   // Prepare the initialization string for an EA with a group of one strategy and the risk manager
   string expertParams = StringFormat(
                             "class CVirtualAdvisor|%s|\\n"
                             "%s|%s|%d|\"SimpleVolumeSingle\", true"
                             ")",
                             groupParams,
                             riskManagerParams, magic, "SimpleVolumeSingle", true
                          );

   PrintFormat(__FUNCTION__" | Expert Params:\\n%s", expertParams);

   // Create an EA handling virtual positions
   expert = NEW(expertParams);

   if(!expert) return(INIT_FAILED);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
   expert.Tick();
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if(!expert) delete expert;
}

//+------------------------------------------------------------------+
//| Test results                                                     |
//+------------------------------------------------------------------+
double OnTester(void) {
   return expert.Tester();
}

//+------------------------------------------------------------------+
//| Initialization before optimization                               |
//+------------------------------------------------------------------+
int OnTesterInit(void) {
   return CVirtualAdvisor::TesterInit(idTask, fileName_);
}

//+------------------------------------------------------------------+
//| Actions after optimization pass                                  |
//+------------------------------------------------------------------+
void OnTesterPass() {
   CVirtualAdvisor::TesterPass();
}

//+------------------------------------------------------------------+
//| Actions after optimization                                       |
//+------------------------------------------------------------------+
void OnTesterDeinit(void) {
   CVirtualAdvisor::TesterDeinit();
}
'''

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(template_content)
            print(f"  ✅ Created clean template for {file_path.name}")
            return True
        except Exception as e:
            print(f"  ❌ Failed to create template: {e}")
            return False

    return False

def copy_fixed_files():
    """Copy fixed files to terminal."""

    print("\n📁 Copying Fixed Files to Terminal")
    print("=" * 40)

    # Copy SimpleVolumesStrategy.mqh
    src = Path("SimpleVolumesStrategy.mqh")
    dst = Path(MT5_TERMINAL_PATH) / "Include" / "MultiCurrency" / "SimpleVolumesStrategy.mqh"

    if src.exists():
        dst.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(src, dst)
        print(f"✅ Copied {src.name} to terminal")

    # Copy Expert files
    expert_files = [
        "SimpleVolumesStage1.mq5",
        "SimpleVolumesExpert.mq5",
        "SimpleVolumesStage2.mq5",
        "SimpleVolumesStage3.mq5"
    ]

    for expert_file in expert_files:
        src = Path("MQL5/Experts") / expert_file
        dst = Path(MT5_TERMINAL_PATH) / "Experts" / expert_file

        if src.exists():
            shutil.copy2(src, dst)
            print(f"✅ Copied {expert_file} to terminal")

if __name__ == "__main__":
    fix_all_mql_files()
    copy_fixed_files()
    
    print("\n🎉 File encoding fix completed!")
    print("\nNext steps:")
    print("1. Open MetaEditor (F4 in MT5)")
    print("2. Recompile SimpleVolumesStage1.mq5")
    print("3. Check for compilation errors")
    print("4. Re-run the optimization")
